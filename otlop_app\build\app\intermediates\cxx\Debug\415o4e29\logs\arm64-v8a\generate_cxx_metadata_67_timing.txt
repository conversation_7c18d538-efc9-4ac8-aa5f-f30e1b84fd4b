# C/C++ build system timings
generate_cxx_metadata
  [gap of 27ms]
  create-invalidation-state 87ms
  [gap of 42ms]
  write-metadata-json-to-file 13ms
generate_cxx_metadata completed in 170ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 143ms]
  create-invalidation-state 129ms
  [gap of 50ms]
  write-metadata-json-to-file 15ms
generate_cxx_metadata completed in 338ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 23ms]
  create-invalidation-state 112ms
  [gap of 38ms]
  write-metadata-json-to-file 15ms
generate_cxx_metadata completed in 189ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 34ms]
  create-invalidation-state 125ms
  [gap of 48ms]
  write-metadata-json-to-file 20ms
generate_cxx_metadata completed in 229ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 20ms]
  create-invalidation-state 64ms
  [gap of 29ms]
  write-metadata-json-to-file 10ms
generate_cxx_metadata completed in 124ms


const { Sequelize , where, Op } = require('sequelize');
const sequelize = require('../config/database');
const db = require('../models/init-models')(sequelize);
const { Order, Cart, ProductCart, Product, IngredientUsage, Ingredient, Restaurant, Customer, Restaurant_Category_Product, DriverModerate, Driver, WorkingTimes } = db;
const { getPagination, getPagingData } = require('../utils/pagination');

// Helper function to get current Libyan time (UTC+2)
function getLibyanTime() {
    const now = new Date();
    // Convert to Libyan time (UTC+2)
    const libyanTime = new Date(now.getTime() + (2 * 60 * 60 * 1000));
    return libyanTime;
}

// Helper function to get current day and time in Libyan timezone
function getLibyanDayAndTime() {
    const libyanTime = getLibyanTime();
    const currentDay = libyanTime.getUTCDay(); // Use UTC methods since we already adjusted for timezone
    const currentTime = libyanTime.toISOString().substring(11, 19); // HH:MM:SS format

    return { currentDay, currentTime };
}

// Helper function to compare times properly (same as restaurantController.js)
function isTimeInRange(currentTime, openTime, closeTime) {
    // Helper function to convert time to string format
    function timeToString(time) {
        if (!time) return null;

        // If it's already a string, return it
        if (typeof time === 'string') {
            return time;
        }

        // If it's a Date object, extract time portion
        if (time instanceof Date) {
            return time.toTimeString().substring(0, 8); // HH:MM:SS
        }

        // If it has toString method, use it
        if (time && typeof time.toString === 'function') {
            return time.toString();
        }

        return null;
    }

    // Convert all times to strings
    const currentStr = timeToString(currentTime);
    const openStr = timeToString(openTime);
    const closeStr = timeToString(closeTime);

    // Check if any conversion failed
    if (!currentStr || !openStr || !closeStr) {
        return false;
    }

    // Convert time strings to comparable format (remove seconds if present)
    const current = currentStr.substring(0, 5); // HH:MM
    const open = openStr.substring(0, 5); // HH:MM
    const close = closeStr.substring(0, 5); // HH:MM

    if (close > open) {
        // Normal case: opens and closes on same day (e.g., 09:00 - 22:00)
        return current >= open && current <= close;
    } else {
        // Special case: closes after midnight (e.g., 18:00 - 02:00)
        return current >= open || current <= close;
    }
}

// Helper function to check if restaurant is open (matches getAllRestaurants logic)
async function isRestaurantOpen(restaurantId) {
    try {
        const { currentDay, currentTime } = getLibyanDayAndTime();
        console.log(`Checking restaurant ${restaurantId} - Current day: ${currentDay}, Current time: ${currentTime}`);

        // Get restaurant with working times
        const restaurant = await Restaurant.findOne({
            where: {
                RestaurantID: restaurantId,
                Status: 1 // Active restaurant
            },
            include: [{
                model: WorkingTimes,
                as: 'WorkingTimes',
                where: {
                    DayOfWeek: currentDay
                },
                required: false
            }]
        });

        console.log(`Restaurant found: ${restaurant ? 'Yes' : 'No'}`);
        if (restaurant) {
            console.log(`Restaurant ${restaurantId} - Status: ${restaurant.Status}, Stats: ${restaurant.Stats}`);
            console.log(`Working times found: ${restaurant.WorkingTimes ? restaurant.WorkingTimes.length : 0}`);
        }

        if (!restaurant) {
            return { isOpen: false, reason: 'Restaurant not found or inactive' };
        }

        console.log(`Restaurant ${restaurantId} Stats: ${restaurant.Stats}`);

        const todayWorkingTime = restaurant.WorkingTimes && restaurant.WorkingTimes[0];
        let isClosed = false;

        if (!todayWorkingTime) {
            console.log(`Restaurant ${restaurantId} has no working time for day ${currentDay}`);
            isClosed = true;
        } else {
            // Check if restaurant is explicitly closed for the day
            if (todayWorkingTime.IsClosed === true) {
                isClosed = true;
                console.log(`Restaurant ${restaurantId} is explicitly closed today`);
            }
            // Check if opening/closing times are null (closed)
            else if (todayWorkingTime.OpeningTime === null || todayWorkingTime.ClosingTime === null) {
                isClosed = true;
                console.log(`Restaurant ${restaurantId} has null opening/closing times`);
            }
            // Check if current time is within opening hours
            else {
                const openTime = todayWorkingTime.OpeningTime;
                const closeTime = todayWorkingTime.ClosingTime;

                // Use the same helper function as getAllRestaurants
                const isOpen = isTimeInRange(currentTime, openTime, closeTime);
                isClosed = !isOpen;

                console.log(`Restaurant ${restaurantId}: Current time ${currentTime}, Open: ${openTime}, Close: ${closeTime}, IsClosed: ${isClosed}`);
            }
        }

        // Final check: restaurant is open if Stats === 1 AND not closed
        const isOperational = restaurant.Stats === 1 && !isClosed;

        if (!isOperational) {
            return {
                isOpen: false,
                reason: isClosed ? 'Restaurant is closed during current hours' : 'Restaurant is temporarily closed',
                currentTime,
                workingTime: todayWorkingTime
            };
        }

        return { isOpen: true };

    } catch (error) {
        console.error('Error checking restaurant status:', error);
        return { isOpen: false, reason: 'Error checking restaurant status' };
    }
}

// Helper function to calculate distance between two points
function calculateDistance(lat1, lon1, lat2, lon2) {
    const R = 6371e3; // Earth's radius in meters
    const φ1 = lat1 * Math.PI / 180;
    const φ2 = lat2 * Math.PI / 180;
    const Δφ = (lat2 - lat1) * Math.PI / 180;
    const Δλ = (lon2 - lon1) * Math.PI / 180;

    const a = Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
        Math.cos(φ1) * Math.cos(φ2) *
        Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

    return R * c;
}

// Helper function to round to nearest 0.25
function roundToQuarter(num) {
    return Math.round(num * 4) / 4;
}

// Helper function to calculate and update driver profit when order is completed
async function calculateDriverProfit(orderId, driverId) {
    try {
        console.log(`Calculating driver profit for Order ${orderId}, Driver ${driverId}`);

        // Get order details with cart information
        const order = await Order.findByPk(orderId, {
            include: [{
                model: Cart,
                as: 'Cart',
                attributes: ['TotalPrice', 'RestaurantID']
            }]
        });

        if (!order || !order.Cart) {
            console.log('Order or Cart not found');
            return;
        }

        // Get restaurant coordinates
        const restaurant = await Restaurant.findByPk(order.Cart.RestaurantID);
        if (!restaurant || !restaurant.latitude || !restaurant.longitude) {
            console.log('Restaurant coordinates not found');
            return;
        }

        // Calculate distance between restaurant and customer
        let distanceInKm = 0;
        if (order.StartPointLatitude && order.StartPointLongitude &&
            order.EndPointLatitude && order.EndPointLongitude) {
            distanceInKm = calculateDistance(
                order.StartPointLatitude,
                order.StartPointLongitude,
                order.EndPointLatitude,
                order.EndPointLongitude
            ) / 1000; // Convert to kilometers
        }

        console.log(`Distance calculated: ${distanceInKm.toFixed(2)} km`);

        // Get driver moderate settings
        const driverModerate = await DriverModerate.findByPk(1);
        if (!driverModerate) {
            console.log('DriverModerate settings not found');
            return;
        }

        // Calculate driving cost based on DriverModerate settings
        let drivingCost = 0;
        if (driverModerate.IsConst) {
            // Use constant value
            drivingCost = driverModerate.ConstValue || 0;
        } else {
            // Calculate based on distance
            drivingCost = distanceInKm * (driverModerate.CostPerKilometer || 0);
        }

        console.log(`Driving cost calculated: ${drivingCost}`);

        // Calculate company profit from driving cost
        const companyProfitPercent = driverModerate.CompanyProfitPercent || 0;
        const companyProfit = (drivingCost * companyProfitPercent) / 100;
        const driverProfit = drivingCost - companyProfit;

        console.log(`Company profit: ${companyProfit} (${companyProfitPercent}%)`);
        console.log(`Driver profit: ${driverProfit}`);

        // Get current driver data
        const driver = await Driver.findByPk(driverId);
        if (!driver) {
            console.log('Driver not found');
            return;
        }

        // Update driver wallet and profit
        const currentWallet = parseFloat(driver.Wallet) || 0;
        const currentProfit = parseFloat(driver.Profit) || 0;

        const newWallet = currentWallet + driverProfit;
        const newProfit = currentProfit + driverProfit;

        await driver.update({
            Wallet: newWallet,
            Profit: newProfit,
            LastWalletUpdate: Sequelize.fn('GETDATE')
        });

        console.log(`Driver wallet updated: ${currentWallet} -> ${newWallet}`);
        console.log(`Driver profit updated: ${currentProfit} -> ${newProfit}`);

        return {
            drivingCost,
            companyProfit,
            driverProfit,
            newWallet,
            newProfit
        };

    } catch (error) {
        console.error('Error calculating driver profit:', error);
        throw error;
    }
}



exports.getAllOrders = async(req, res) => {
    try {
        const { page, size } = req.query;
        const { limit, offset } = getPagination(page, size);

        const orders = await Order.findAndCountAll({
            limit,
            offset,
            order: [['OrderDate', 'DESC']],
            include: [
                {
                    model: Cart,
                    as: 'Cart',
                    attributes: ['TotalPrice'],
                },
                {
                    model: Driver,
                    as: 'Driver',
                    attributes: ['FirstName', 'LastName'],
                    required: false
                }
            ]
        });

        const formattedOrders = orders.rows.map(async order => {
            const plainOrder = order.get({ plain: true });

            const {
                StartPointLatitude,
                StartPointLongitude,
                EndPointLatitude,
                EndPointLongitude,
                ...orderData
            } = plainOrder;

            let restaurantName = null;
            let cartTotalPrice = null;
            let driverFirstName = null;
            let driverLastName = null;

            if (order.CartID) {
                const cart = await Cart.findByPk(order.CartID);
                if (cart) {
                    cartTotalPrice = cart.TotalPrice;
                    if (cart.RestaurantID) {
                        const restaurant = await Restaurant.findByPk(cart.RestaurantID);
                        if (restaurant) {
                            restaurantName = restaurant.Name;
                        }
                    }
                }
            }

            if (order.Driver) {
                driverFirstName = order.Driver.FirstName;
                driverLastName = order.Driver.LastName;
            }

            let distanceInMeters = null;
            if (
                StartPointLatitude != null &&
                StartPointLongitude != null &&
                EndPointLatitude != null &&
                EndPointLongitude != null
            ) {
                distanceInMeters = calculateDistance(
                    StartPointLatitude,
                    StartPointLongitude,
                    EndPointLatitude,
                    EndPointLongitude
                );
            }

            return {
                ...orderData,
                RestaurantName: restaurantName,
                CartTotalPrice: cartTotalPrice,
                DriverFirstName: driverFirstName,
                DriverLastName: driverLastName,
                DistanceInMeters: distanceInMeters
            };
        });

        const resolvedFormattedOrders = await Promise.all(formattedOrders);

        const response = getPagingData({ rows: resolvedFormattedOrders, count: orders.count }, page, limit);
        res.status(200).json(response);

    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Error fetching orders',
            error: error.message
        });
    }
};


exports.getOrderDetails = async(req, res) => {
    try {
        const order = await Order.findByPk(req.params.orderId, {
            include: [
                {
                    model: Cart,
                    as: 'Cart',
                    include: [{
                        model: ProductCart,
                        as: 'ProductCarts',
                        include: [{
                                model: Product,
                                as: 'Product',
                                attributes: ['ProductName', 'Price']
                            },
                            {
                                model: IngredientUsage,
                                as: 'IngredientUsages',
                                include: [{
                                    model: Ingredient,
                                    as: 'Ingredient',
                                    attributes: ['IngredientName']
                                }]
                            }
                        ]
                    }]
                },
                {
                    model: Customer,
                    as: 'Customer',
                    attributes: ['CustomerID', 'PhoneNum']
                },
                {
                    model: Driver,
                    as: 'Driver',
                    attributes: ['DriverID', 'FirstName', 'LastName', 'PhoneNumber']
                },
                {
                    model: db.Invoice,
                    as: 'Invoices',
                    required: false,
                    attributes: ['InvoiceID']
                }
            ]
        });

        if (!order) {
            return res.status(404).json({ message: 'Order not found' });
        }

        let restaurant = null;
        if (order.Cart) {
            restaurant = await Restaurant.findByPk(order.Cart.RestaurantID, {
                attributes: ['RestaurantID', 'Name', 'Image', 'City', 'Address']
            });
        }

        const orderData = order.get({ plain: true });

        const formattedProducts = orderData.Cart ? orderData.Cart.ProductCarts.map(pc => ({
            ProductName: pc.Product.ProductName,
            Price: pc.Product.Price,
            Quantity: pc.Quantity,
            Ingredients: pc.IngredientUsages.map(iu => iu.Ingredient.IngredientName)
        })) : [];

        const formattedOrder = {
            ...orderData,
            Cart: {
                ...orderData.Cart,
                Restaurant: restaurant,
                Products: formattedProducts
            },
            Location: {
                StartPoint: {
                    Latitude: orderData.StartPointLatitude,
                    Longitude: orderData.StartPointLongitude
                },
                EndPoint: {
                    Latitude: orderData.EndPointLatitude,
                    Longitude: orderData.EndPointLongitude
                }
            },
            DistanceInMeters: calculateDistance(
                orderData.StartPointLatitude,
                orderData.StartPointLongitude,
                orderData.EndPointLatitude,
                orderData.EndPointLongitude
            ),
            InvoiceID: order.Invoice ? order.Invoice.InvoiceID : null
        };

        if (formattedOrder.Cart) {
            delete formattedOrder.Cart.ProductCarts;
        }
        delete formattedOrder.StartPointLatitude;
        delete formattedOrder.StartPointLongitude;
        delete formattedOrder.EndPointLatitude;
        delete formattedOrder.EndPointLongitude;

        res.json(formattedOrder);
    } catch (error) {
        console.error('Error in getOrderDetails:', error);
        res.status(500).json({ message: 'Error fetching order details' });
    }
};

exports.getOrderCart = async(req, res) => {
    try {
        const order = await Order.findByPk(req.params.orderId);
        if (!order) {
            return res.status(404).json({ message: 'لم يتم العثور على الطلب' });
        }

        const cart = await Cart.findByPk(order.CartID, {
            attributes: ['CartID', 'RestaurantID', 'CustomerID', 'TotalPrice', 'CartDate'],
            include: [{
                model: ProductCart,
                as: 'ProductCarts',
                include: [{
                        model: Product,
                        as: 'Product',
                        attributes: ['ProductName', 'Price']
                    },
                    {
                        model: IngredientUsage,
                        as: 'IngredientUsages',
                        include: [{
                            model: Ingredient,
                            as: 'Ingredient',
                            attributes: ['IngredientName']
                        }]
                    }
                ]
            }]
        });

        if (!cart) {
            return res.status(404).json({ message: 'لم يتم العثور على السلة' });
        }

        const response = {
            cartDetails: {
                cartId: cart.CartID,
                restaurantId: cart.RestaurantID,
                customerId: cart.CustomerID,
                totalPrice: cart.TotalPrice,
                cartDate: cart.CartDate
            },
            products: cart.ProductCarts.map(pc => ({
                productName: pc.Product.ProductName,
                price: pc.Product.Price,
                quantity: pc.Quantity,
                ingredients: pc.IngredientUsages.map(iu => ({
                    name: iu.Ingredient.IngredientName,
                    quantity: iu.Quantity
                }))
            }))
        };

        res.json(response);
    } catch (error) {
        console.error('Error in getOrderCart:', error);
        res.status(500).json({ message: error.message + 'حدث خطأ أثناء جلب تفاصيل السلة' });
    }
};

exports.getRestaurantOrders = async(req, res) => {
    try {
        const { restaurantId } = req.params;
        const { page, size } = req.query;
        const { limit, offset } = getPagination(page, size);

        console.log(`getRestaurantOrders: Fetching orders for restaurant ID: ${restaurantId}`);

        // First get all cart IDs for this restaurant
        const restaurantCarts = await Cart.findAll({
            where: { RestaurantID: Number(restaurantId) },
            attributes: ['CartID']
        });

        const cartIds = restaurantCarts.map(cart => cart.CartID);
        console.log(`getRestaurantOrders: Found ${cartIds.length} carts for restaurant`);

        if (cartIds.length === 0) {
            console.log(`getRestaurantOrders: No carts found for restaurant ${restaurantId}`);
            const response = getPagingData({ rows: [], count: 0 }, page, limit);
            return res.status(200).json(response);
        }

        // Now get orders with pagination, with proper associations
        const orders = await Order.findAndCountAll({
            where: {
                CartID: cartIds
            },
            limit,
            offset,
            order: [['OrderDate', 'DESC']],
            include: [
                {
                    model: Cart,
                    as: 'Cart',
                    attributes: ['TotalPrice'],
                },
                {
                    model: Driver,
                    as: 'Driver',
                    attributes: ['FirstName', 'LastName'],
                    required: false
                }
            ]
        });

        console.log(`getRestaurantOrders: Found ${orders.count} total orders`);

        const formattedOrders = orders.rows.map(async order => {
            const plainOrder = order.get({ plain: true });

            const {
                StartPointLatitude,
                StartPointLongitude,
                EndPointLatitude,
                EndPointLongitude,
                ...orderData
            } = plainOrder;

            let restaurantName = null;
            let cartTotalPrice = null;
            let driverFirstName = null;
            let driverLastName = null;
            let customerPhoneNum = null;
            let productCarts = [];

            if (order.CartID) {
                const cart = await Cart.findByPk(order.CartID, {
                    include: [
                        {
                            model: ProductCart,
                            as: 'ProductCarts',
                            include: [
                                {
                                    model: Product,
                                    as: 'Product',
                                    attributes: ['ProductID', 'ProductName', 'Price', 'Image']
                                },
                                {
                                    model: IngredientUsage,
                                    as: 'IngredientUsages',
                                    include: [
                                        {
                                            model: Ingredient,
                                            as: 'Ingredient',
                                            attributes: ['IngredientID', 'IngredientName']
                                        }
                                    ],
                                    required: false
                                }
                            ]
                        }
                    ]
                });

                if (cart) {
                    cartTotalPrice = cart.TotalPrice;
                    productCarts = cart.ProductCarts || [];

                    if (cart.RestaurantID) {
                        const restaurant = await Restaurant.findByPk(cart.RestaurantID);
                        if (restaurant) {
                            restaurantName = restaurant.Name;
                        }
                    }
                }
            }

            if (order.Driver) {
                driverFirstName = order.Driver.FirstName;
                driverLastName = order.Driver.LastName;
            }

            // Get customer phone number
            if (order.CustomerID) {
                const customer = await Customer.findByPk(order.CustomerID, {
                    attributes: ['PhoneNum']
                });
                if (customer) {
                    customerPhoneNum = customer.PhoneNum;
                }
            }

            let distanceInMeters = null;
            if (
                StartPointLatitude != null &&
                StartPointLongitude != null &&
                EndPointLatitude != null &&
                EndPointLongitude != null
            ) {
                distanceInMeters = calculateDistance(
                    StartPointLatitude,
                    StartPointLongitude,
                    EndPointLatitude,
                    EndPointLongitude
                );
            }

            return {
                ...orderData,
                RestaurantName: restaurantName,
                CartTotalPrice: cartTotalPrice,
                DriverFirstName: driverFirstName,
                DriverLastName: driverLastName,
                CustomerPhoneNum: customerPhoneNum,
                DistanceInMeters: distanceInMeters,
                Products: productCarts
            };
        });

        const resolvedFormattedOrders = await Promise.all(formattedOrders);

        const response = getPagingData({ rows: resolvedFormattedOrders, count: orders.count }, page, limit);
        res.status(200).json(response);

    } catch (error) {
        console.error('Error in getRestaurantOrders:', error);
        res.status(500).json({
            success: false,
            message: 'Error fetching restaurant orders',
            error: error.message
        });
    }
};




exports.getDriverOrders = async(req, res) => {
    try {
        const orders = await Order.findAll({
            where: { DriverID: req.params.driverId },
            include: [{
                model: Cart,
                as: 'Cart',
                attributes: ['CartID', 'TotalPrice', 'CartDate']
            }]
        });

        res.json(orders);
    } catch (error) {
        console.error('Error in getDriverOrders:', error);
        res.status(500).json({ message: 'حدث خطأ أثناء جلب طلبات السائق' });
    }
};

exports.editOrder = async(req, res) => {
    try {
        const order = await Order.findByPk(req.params.orderId);

        if (!order) {
            return res.status(404).json({ message: 'لم يتم العثور على الطلب' });
        }

        // Store old status to check if it changed
        const oldStatus = order.Status;
        const newStatus = req.body.Status;

        await order.update(req.body);

        // Check if order is completed and calculate driver profit
        if (oldStatus !== newStatus &&
            order.DriverID &&
            (newStatus === 3 || newStatus === 5 || newStatus === 7)) {

            console.log(`Order ${order.OrderID} completed with status ${newStatus}, calculating driver profit...`);

            try {
                const profitResult = await calculateDriverProfit(order.OrderID, order.DriverID);
                console.log('Driver profit calculated successfully:', profitResult);
            } catch (profitError) {
                console.error('Error calculating driver profit:', profitError);
                // Don't fail the order update if profit calculation fails
            }
        }

        const updatedOrder = await Order.findByPk(req.params.orderId, {
            include: [{
                association: 'Driver',
                attributes: ['FirstName', 'LastName'],
                required: false
            }]
        });

        res.json(updatedOrder);
    } catch (error) {
        console.error('Error in editOrder:', error);
        res.status(500).json({ message: 'حدث خطأ أثناء تعديل الطلب' });
    }
};

// Create a new order
exports.createOrder = async (req, res) => {
    try {
        console.log('Creating order with data:', JSON.stringify(req.body, null, 2));

        const {
            customerId,
            items,
            address,
            paymentMethod,
            note,
            endPointLatitude,
            endPointLongitude
        } = req.body;

        // Validate required fields
        if (!customerId || !items || !Array.isArray(items) || items.length === 0) {
            console.log('Validation failed: Missing customerId or items');
            return res.status(400).json({
                success: false,
                message: 'Customer ID and items are required'
            });
        }

        console.log(`Processing order for customer ${customerId} with ${items.length} items`);

        // Get the first item to determine restaurant
        const firstItem = items[0];
        let restaurantId = parseInt(firstItem.restaurantId);
        console.log(`Restaurant ID from first item: ${restaurantId}`);

        // If restaurant ID is 0 or invalid, try to get it from the product
        if (!restaurantId || restaurantId === 0 || isNaN(restaurantId)) {
            console.log('Restaurant ID is invalid, trying to get from product...');

            // Get the product to find its restaurant
            const product = await Product.findOne({
                where: { ProductID: firstItem.productId },
                include: [{
                    model: Restaurant_Category_Product,
                    as: 'Restaurant_Category_Products',
                    attributes: ['RestaurantID']
                }]
            });

            if (product && product.Restaurant_Category_Products && product.Restaurant_Category_Products.length > 0) {
                restaurantId = product.Restaurant_Category_Products[0].RestaurantID;
                console.log(`Found restaurant ID from product: ${restaurantId}`);
            } else {
                console.log('Could not determine restaurant ID from product');
                return res.status(400).json({
                    success: false,
                    message: 'Could not determine restaurant for this order. Please try again.'
                });
            }
        }

        // Validate all items are from the same restaurant (if they have restaurant IDs)
        const itemsWithRestaurantIds = items.filter(item => item.restaurantId && parseInt(item.restaurantId) !== 0);
        if (itemsWithRestaurantIds.length > 0) {
            const allSameRestaurant = itemsWithRestaurantIds.every(item => parseInt(item.restaurantId) === restaurantId);
            if (!allSameRestaurant) {
                console.log('Validation failed: Items from different restaurants');
                return res.status(400).json({
                    success: false,
                    message: 'All items must be from the same restaurant'
                });
            }
        }

        // Verify restaurant exists
        const restaurant = await Restaurant.findByPk(restaurantId);
        if (!restaurant) {
            console.log(`Validation failed: Restaurant ${restaurantId} not found`);
            return res.status(400).json({
                success: false,
                message: 'Restaurant not found'
            });
        }

        // Check if restaurant is open and accepting orders
        console.log(`Checking if restaurant ${restaurantId} is open...`);
        const restaurantStatus = await isRestaurantOpen(restaurantId);
        if (!restaurantStatus.isOpen) {
            console.log(`Restaurant ${restaurantId} is closed: ${restaurantStatus.reason}`);
            return res.status(400).json({
                success: false,
                message: 'المطعم مغلق حالياً',
                details: restaurantStatus.reason,
                openTime: restaurantStatus.openTime,
                closeTime: restaurantStatus.closeTime
            });
        }
        console.log(`Restaurant ${restaurantId} is open and accepting orders`);

        // Calculate total price
        let totalPrice = 0;
        for (const item of items) {
            totalPrice += item.price * item.quantity;
        }

        // Get customer location for delivery fee calculation
        console.log('Getting customer location...');
        const customer = await Customer.findByPk(customerId);
        let customerLatitude = endPointLatitude;
        let customerLongitude = endPointLongitude;

        // If coordinates not provided in request, try to get from customer profile
        if (!customerLatitude || !customerLongitude) {
            if (customer && customer.LocationLatitude && customer.LocationLongitude) {
                customerLatitude = customer.LocationLatitude;
                customerLongitude = customer.LocationLongitude;
                console.log(`Using customer profile location: ${customerLatitude}, ${customerLongitude}`);
            }
        }

        // Get restaurant coordinates
        console.log('Getting restaurant coordinates...');
        const restaurantLatitude = restaurant.latitude;
        const restaurantLongitude = restaurant.longitude;

        if (!restaurantLatitude || !restaurantLongitude) {
            console.log(`Restaurant ${restaurantId} has no coordinates`);
            return res.status(400).json({
                success: false,
                message: 'Restaurant location not available'
            });
        }

        // Calculate delivery fee based on DriverModerate settings
        let deliveryFee = 0;
        let distanceInKm = 0;

        if (customerLatitude && customerLongitude) {
            // Calculate distance between restaurant and customer
            distanceInKm = calculateDistance(
                restaurantLatitude,
                restaurantLongitude,
                customerLatitude,
                customerLongitude
            ) / 1000; // Convert to kilometers

            console.log(`Distance calculated: ${distanceInKm.toFixed(2)} km`);

            // Get driver moderate settings
            const driverModerate = await DriverModerate.findByPk(1);
            if (driverModerate) {
                if (driverModerate.IsConst) {
                    // Use constant delivery fee
                    deliveryFee = roundToQuarter(driverModerate.ConstValue || 0);
                    console.log(`Using constant delivery fee: ${deliveryFee}`);
                } else {
                    // Calculate based on distance
                    deliveryFee = roundToQuarter(distanceInKm * (driverModerate.CostPerKilometer || 0));
                    console.log(`Calculated delivery fee: ${deliveryFee} (${distanceInKm.toFixed(2)} km × ${driverModerate.CostPerKilometer})`);
                }
            } else {
                console.log('No driver moderate settings found, using default fee');
                deliveryFee = roundToQuarter(5.0); // Fallback
            }
        } else {
            console.log('No customer coordinates available, using default delivery fee');
            deliveryFee = roundToQuarter(5.0); // Fallback when no coordinates
        }

        totalPrice = roundToQuarter(totalPrice + deliveryFee);
        console.log(`Total price calculated: ${totalPrice} (including delivery fee: ${deliveryFee})`);

        // Calculate order price (total without delivery fee)
        const orderPrice = roundToQuarter(totalPrice - deliveryFee);
        console.log(`Order price (without delivery): ${orderPrice}`);

        // Create cart
        console.log('Creating cart...');
        const cart = await Cart.create({
            RestaurantID: restaurantId,
            CustomerID: customerId,
            TotalPrice: totalPrice,
            CartDate: Sequelize.fn('GETDATE')
        });
        console.log(`Cart created with ID: ${cart.CartID}`);

        // Create cart items
        console.log('Creating cart items...');
        for (const item of items) {
            console.log(`Adding item: ${item.productName} (ID: ${item.productId}) x${item.quantity}`);

            // Validate product ID
            const productId = parseInt(item.productId);
            if (!productId || productId === 0 || isNaN(productId)) {
                console.log(`Invalid product ID: ${item.productId}`);
                return res.status(400).json({
                    success: false,
                    message: `Invalid product ID: ${item.productId}`
                });
            }

            // Verify product exists
            const product = await Product.findByPk(productId);
            if (!product) {
                console.log(`Product ${productId} not found`);
                return res.status(400).json({
                    success: false,
                    message: `Product not found: ${item.productName}`
                });
            }

            // Validate quantity
            const quantity = parseInt(item.quantity);
            if (!quantity || quantity <= 0 || isNaN(quantity)) {
                console.log(`Invalid quantity: ${item.quantity}`);
                return res.status(400).json({
                    success: false,
                    message: `Invalid quantity for ${item.productName}`
                });
            }

            // Create ProductCart using proper Sequelize model
            let productCart;
            try {
                productCart = await ProductCart.create({
                    CartID: cart.CartID,
                    ProductID: productId,
                    Quantity: quantity
                });
                console.log(`ProductCart created with ID: ${productCart.ProductCartID}, Quantity: ${quantity}`);
            } catch (productCartError) {
                console.error('Error creating ProductCart:', productCartError);
                return res.status(500).json({
                    success: false,
                    message: 'Failed to create cart item',
                    error: productCartError.message
                });
            }

            // Handle ingredients if any
            if (item.selectedIngredients && item.selectedIngredients.length > 0) {
                console.log(`Adding ${item.selectedIngredients.length} ingredients for product ${item.productId}`);
                for (const ingredient of item.selectedIngredients) {
                    // Parse ingredient ID to ensure it's an integer
                    const ingredientId = parseInt(ingredient.id);
                    if (isNaN(ingredientId)) {
                        console.warn(`Invalid ingredient ID: ${ingredient.id}, skipping...`);
                        continue;
                    }

                    try {
                        // Verify ingredient exists and belongs to the product
                        const ingredientExists = await Ingredient.findOne({
                            where: {
                                IngredientID: ingredientId,
                                ProductID: productId
                            }
                        });

                        if (!ingredientExists) {
                            console.warn(`Ingredient ${ingredientId} not found for product ${productId}, skipping...`);
                            continue;
                        }

                        // Create IngredientUsage using proper Sequelize model
                        await IngredientUsage.create({
                            ProductCartID: productCart.ProductCartID,
                            IngredientID: ingredientId,
                            IsNeeded: true,
                            Quantity: ingredient.quantity || 1
                        });
                        console.log(`Added ingredient ${ingredient.name} (ID: ${ingredientId}) with quantity: ${ingredient.quantity || 1}`);
                    } catch (ingredientError) {
                        console.error(`Error adding ingredient ${ingredient.name}:`, ingredientError);
                        // Continue with other ingredients instead of failing the entire order
                    }
                }
            }
        }

        // Calculate driver profit based on DriverModerate settings
        let driverProfit = 0;
        const driverModerate = await DriverModerate.findByPk(1);
        if (driverModerate && driverModerate.CompanyProfitPercent) {
            // Driver gets the remaining percentage after company takes its cut
            const driverProfitPercent = 100 - driverModerate.CompanyProfitPercent;
            driverProfit = roundToQuarter((deliveryFee * driverProfitPercent) / 100);
        } else {
            // Default: driver gets 70% of delivery fee
            driverProfit = roundToQuarter(deliveryFee * 0.7);
        }
        console.log(`Driver profit calculated: ${driverProfit} (from delivery fee: ${deliveryFee})`);

        // Create order with correct coordinates and calculated values
        // StartPoint = Restaurant (pickup location)
        // EndPoint = Customer (delivery location)
        console.log('Creating order...');
        const order = await Order.create({
            DriverID: null, // Will be assigned later
            CartID: cart.CartID,
            CustomerID: customerId,
            StartPointLatitude: restaurantLatitude,      // Restaurant coordinates
            StartPointLongitude: restaurantLongitude,    // Restaurant coordinates
            EndPointLatitude: customerLatitude || null,  // Customer coordinates
            EndPointLongitude: customerLongitude || null, // Customer coordinates
            Duration: null, // Will be calculated when driver is assigned
            OrderDate: Sequelize.fn('GETDATE'),
            Status: 0, // Pending status
            Note: note || null,
            Address: address || null,
            PaymentMethod: paymentMethod || 'cash',
            // New calculated fields
            TotalPrice: totalPrice,
            Distance: roundToQuarter(distanceInKm),
            DriverProfit: driverProfit,
            OrderPrice: orderPrice,
            DeliveryFee: deliveryFee
        });
        console.log(`Order created with ID: ${order.OrderID} - TotalPrice: ${totalPrice}, Distance: ${roundToQuarter(distanceInKm)}km, DeliveryFee: ${deliveryFee}, DriverProfit: ${driverProfit}`);

        console.log('Order created successfully');

        console.log('Order created successfully, sending response...');
        res.status(201).json({
            success: true,
            message: 'Order created successfully',
            OrderID: order.OrderID.toString(),
            data: {
                orderId: order.OrderID,
                cartId: cart.CartID,
                totalPrice: totalPrice,
                status: order.Status,
                orderDate: order.OrderDate
            }
        });

    } catch (error) {
        console.error('Error creating order:', error);
        console.error('Error stack:', error.stack);

        // Handle specific error types
        let statusCode = 500;
        let message = 'Failed to create order';

        if (error.name === 'SequelizeValidationError') {
            statusCode = 400;
            message = 'Validation error: ' + error.errors.map(e => e.message).join(', ');
        } else if (error.name === 'SequelizeForeignKeyConstraintError') {
            statusCode = 400;
            message = 'Invalid reference: ' + error.message;
        }

        res.status(statusCode).json({
            success: false,
            message: message,
            error: error.message,
            details: process.env.NODE_ENV === 'development' ? error.stack : undefined
        });
    }
};

// Calculate delivery fee endpoint
exports.calculateDeliveryFee = async (req, res) => {
    try {
        const {
            restaurantLatitude,
            restaurantLongitude,
            customerLatitude,
            customerLongitude
        } = req.body;

        // Validate required fields
        if (!restaurantLatitude || !restaurantLongitude || !customerLatitude || !customerLongitude) {
            return res.status(400).json({
                success: false,
                message: 'All coordinates are required'
            });
        }

        // Calculate distance between restaurant and customer
        const distanceInKm = calculateDistance(
            restaurantLatitude,
            restaurantLongitude,
            customerLatitude,
            customerLongitude
        ) / 1000; // Convert to kilometers

        // Get driver moderate settings
        let deliveryFee = 0;
        const driverModerate = await DriverModerate.findByPk(1);
        if (driverModerate) {
            if (driverModerate.IsConst) {
                // Use constant delivery fee
                deliveryFee = roundToQuarter(driverModerate.ConstValue || 0);
            } else {
                // Calculate based on distance
                deliveryFee = roundToQuarter(distanceInKm * (driverModerate.CostPerKilometer || 0));
            }
        } else {
            deliveryFee = roundToQuarter(5.0); // Fallback
        }

        res.json({
            success: true,
            deliveryFee: deliveryFee,
            distance: roundToQuarter(distanceInKm)
        });

    } catch (error) {
        console.error('Error calculating delivery fee:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to calculate delivery fee',
            error: error.message
        });
    }
};

// Get available orders (Status = 1, no driver assigned)
exports.getAvailableOrders = async(req, res) => {
    try {
        const { page, size } = req.query;
        const { limit, offset } = getPagination(page, size);

        const orders = await Order.findAndCountAll({
            where: {
                Status: 1,
                DriverID: null,
                Claimed: false
            },
            limit,
            offset,
            order: [['OrderDate', 'DESC']],
            include: [
                {
                    model: Cart,
                    as: 'Cart',
                    attributes: ['TotalPrice', 'RestaurantID'],
                },
                {
                    model: Customer,
                    as: 'Customer',
                    attributes: ['CustomerID', 'PhoneNum']
                }
            ]
        });

        const formattedOrders = orders.rows.map(async order => {
            const plainOrder = order.get({ plain: true });

            const {
                StartPointLatitude,
                StartPointLongitude,
                EndPointLatitude,
                EndPointLongitude,
                ...orderData
            } = plainOrder;

            let restaurantName = null;
            let cartTotalPrice = null;

            if (order.CartID) {
                const cart = await Cart.findByPk(order.CartID);
                if (cart) {
                    cartTotalPrice = cart.TotalPrice;
                    if (cart.RestaurantID) {
                        const restaurant = await Restaurant.findByPk(cart.RestaurantID);
                        if (restaurant) {
                            restaurantName = restaurant.Name;
                        }
                    }
                }
            }

            let distanceInMeters = null;
            if (
                StartPointLatitude != null &&
                StartPointLongitude != null &&
                EndPointLatitude != null &&
                EndPointLongitude != null
            ) {
                distanceInMeters = calculateDistance(
                    StartPointLatitude,
                    StartPointLongitude,
                    EndPointLatitude,
                    EndPointLongitude
                );
            }

            return {
                ...orderData,
                StartPointLatitude: StartPointLatitude,
                StartPointLongitude: StartPointLongitude,
                EndPointLatitude: EndPointLatitude,
                EndPointLongitude: EndPointLongitude,
                RestaurantName: restaurantName,
                CartTotalPrice: cartTotalPrice,
                DistanceInMeters: distanceInMeters
            };
        });

        const resolvedFormattedOrders = await Promise.all(formattedOrders);

        const response = getPagingData({ rows: resolvedFormattedOrders, count: orders.count }, page, limit);
        res.status(200).json(response);

    } catch (error) {
        res.status(500).json({
            success: false,
            message: ' - - Error fetching available orders',
            error: error
        });
    }
};

// Get customer orders
exports.getCustomerOrders = async(req, res) => {
    try {
        const { customerId } = req.params;
        const { page, size } = req.query;
        const { limit, offset } = getPagination(page, size);

        const orders = await Order.findAndCountAll({
            where: { CustomerID: customerId },
            limit,
            offset,
            order: [['OrderDate', 'DESC']],
            include: [
                {
                    model: Cart,
                    as: 'Cart',
                    attributes: ['TotalPrice', 'CartDate', 'RestaurantID'],
                    include: [
                        {
                            model: ProductCart,
                            as: 'ProductCarts',
                            include: [
                                {
                                    model: Product,
                                    as: 'Product',
                                    attributes: ['ProductID', 'ProductName', 'Price', 'Image']
                                },
                                {
                                    model: IngredientUsage,
                                    as: 'IngredientUsages',
                                    include: [
                                        {
                                            model: Ingredient,
                                            as: 'Ingredient',
                                            attributes: ['IngredientID', 'IngredientName']
                                        }
                                    ],
                                    required: false
                                }
                            ]
                        }
                    ]
                },
                {
                    model: Driver,
                    as: 'Driver',
                    attributes: ['FirstName', 'LastName', 'PhoneNumber'],
                    required: false
                }
            ]
        });

        const formattedOrders = await Promise.all(orders.rows.map(async order => {
            const plainOrder = order.get({ plain: true });

            // Format products with ingredients
            const products = plainOrder.Cart?.ProductCarts?.map(pc => ({
                id: pc.Product.ProductID,
                name: pc.Product.ProductName,
                price: pc.Product.Price,
                image: pc.Product.Image,
                quantity: pc.Quantity,
                ingredients: pc.IngredientUsages?.map(iu => ({
                    id: iu.Ingredient.IngredientID,
                    name: iu.Ingredient.IngredientName,
                    isNeeded: iu.IsNeeded
                })) || []
            })) || [];

            // Get restaurant details separately
            let restaurant = null;
            if (plainOrder.Cart?.RestaurantID) {
                try {
                    const restaurantData = await Restaurant.findByPk(plainOrder.Cart.RestaurantID, {
                        attributes: ['RestaurantID', 'Name', 'Image']
                    });
                    if (restaurantData) {
                        restaurant = {
                            id: restaurantData.RestaurantID,
                            name: restaurantData.Name,
                            image: restaurantData.Image
                        };
                    }
                } catch (err) {
                    console.error('Error fetching restaurant:', err);
                }
            }

            // Calculate distance if coordinates are available
            let distanceInMeters = null;
            if (
                plainOrder.StartPointLatitude &&
                plainOrder.StartPointLongitude &&
                plainOrder.EndPointLatitude &&
                plainOrder.EndPointLongitude
            ) {
                distanceInMeters = calculateDistance(
                    plainOrder.StartPointLatitude,
                    plainOrder.StartPointLongitude,
                    plainOrder.EndPointLatitude,
                    plainOrder.EndPointLongitude
                );
            }

            return {
                OrderID: plainOrder.OrderID,
                Status: plainOrder.Status,
                OrderDate: plainOrder.OrderDate,
                TotalPrice: plainOrder.Cart?.TotalPrice || 0,
                Address: plainOrder.Address,
                PaymentMethod: plainOrder.PaymentMethod,
                Note: plainOrder.Note,
                Restaurant: restaurant,
                Driver: plainOrder.Driver ? {
                    name: `${plainOrder.Driver.FirstName} ${plainOrder.Driver.LastName}`.trim(),
                    phone: plainOrder.Driver.PhoneNumber
                } : null,
                Products: products,
                DistanceInMeters: distanceInMeters,
                Location: {
                    startPoint: {
                        latitude: plainOrder.StartPointLatitude,
                        longitude: plainOrder.StartPointLongitude
                    },
                    endPoint: {
                        latitude: plainOrder.EndPointLatitude,
                        longitude: plainOrder.EndPointLongitude
                    }
                }
            };
        }));

        const response = getPagingData({
            rows: formattedOrders,
            count: orders.count
        }, page, limit);

        res.status(200).json(response);

    } catch (error) {
        console.error('Error fetching customer orders:', error);
        res.status(500).json({
            success: false,
            message: 'Error fetching customer orders',
            error: error.message
        });
    }
};

// Export the profit calculation function for use in other controllers
exports.calculateDriverProfit = calculateDriverProfit;